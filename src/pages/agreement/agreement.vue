<template>
  <view class="agreement-container">
    <!-- 头部区域 - 简化版，使用uni-app原生导航栏 -->

    <!-- 主标题 -->
    <text class="main-title">我在延安有棵苹安树认养规则介绍</text>

    <!-- 协议内容 -->
    <view class="content-section">
      <!-- 介绍部分 -->
      <view class="intro-section">
        <text class="intro-note">以下内容需要自行拟定，仅供参考：</text>

        <view class="paragraph">
          <text class="paragraph-text">
            "金壶口果树认养"是由延安市金壶口向全国倡议发起的公益品牌项目，项目推行"共享经济+定制农业+文旅服务"苹果产售新模式。本平台为您提供的是一项线上认养服务，请您务必仔细阅读、充分理解本规则，除非您已阅读并接受本规则所有条款，否则您无法使用本服务。若您不同意本规则，请停止使用本服务。
          </text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">
            1、本次认养的是2023年1月至12月(后称"认养季")种植在延安市金壶口的苹果树，开放认养数量10万棵，平台将分批开放进行认养。
          </text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">
            2、标准认养单位分为单棵认养一个认养季，果树所属农户均与"金壶口"签订了相关种植服务与苹果收购协议。
          </text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">
            3、根据认养开放区域连续2年的实际产量情况，单棵认养按照树龄分为:幼树、初挂果、挂果树、盛果树，认养收益根据具体产品产量承若为准。
          </text>
        </view>
      </view>

      <!-- 甲方权利 -->
      <view class="section">
        <text class="section-title">甲方（认养人）权利：</text>

        <view class="paragraph">
          <text class="paragraph-text">获得认养果树当季产出的苹果（产量以实际采收为准）；</text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">通过平台专属页面查看果树生长动态（含照片/视频定期更新）；</text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">预约免费果园参观1次（限本人，含基础导览服务）；</text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">获得"金壶口认养证书"电子版及定制礼盒包装服务。</text>
        </view>
      </view>

      <!-- 甲方义务 -->
      <view class="section">
        <text class="section-title">甲方义务：</text>

        <view class="paragraph">
          <text class="paragraph-text">按期支付认养费用；</text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">遵守果园安全管理规定（如参观时需预约并由农户陪同）；</text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">不得干预农户正常种植管理。</text>
        </view>
      </view>

      <!-- 乙方义务 -->
      <view class="section">
        <text class="section-title">乙方（平台）义务：</text>

        <view class="paragraph">
          <text class="paragraph-text">提供果树日常管护（施肥修剪、防虫等标准化种植）；</text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">为每棵认养果树建立电子档案并定期更新；</text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">组织苹果采收、分拣及寄送服务（邮费另计）；</text>
        </view>

        <view class="paragraph">
          <text class="paragraph-text">对因自然灾害导致的减产提供补偿方案（见第五条）。</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 返回按钮处理
const handleBack = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-primary: #0f0f0f;
$text-secondary: #333333;
$text-bold: #333333;
$background-color: #ffffff;

// 尺寸变量
$header-height: 160rpx;
$content-padding: 40rpx;
$text-line-height: 50rpx;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 主容器
.agreement-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: $background-color;
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

// 头部区域 - 已简化，使用原生导航栏

// 主标题
.main-title {
  text-align: center;
  font-size: 38rpx;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  line-height: 45rpx;
  color: $primary-color;
  margin: 40rpx 0 0 0;
  padding: 0 $content-padding;
  display: block;
  width: 100%;
  box-sizing: border-box;
}

// 内容区域
.content-section {
  width: 100%;
  margin: 32rpx 0 0;
  padding: 0 $content-padding $content-padding $content-padding;
  flex: 1;
  box-sizing: border-box;
}

// 介绍部分
.intro-section {
  margin-bottom: 60rpx;
}

.intro-note {
  font-size: 28rpx;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  line-height: $text-line-height;
  font-weight: 400;
  color: $text-secondary;
  margin-bottom: 30rpx;
  display: block;
}

// 章节
.section {
  margin-bottom: 50rpx;
}

.section-title {
  font-size: 32rpx;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  line-height: 45rpx;
  font-weight: 700;
  color: $text-bold;
  margin-bottom: 30rpx;
  display: block;
}

// 段落
.paragraph {
  margin-bottom: 25rpx;
}

.paragraph-text {
  font-size: 28rpx;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  line-height: $text-line-height;
  font-weight: 400;
  color: $text-secondary;
  text-align: justify;
  display: block;
  word-wrap: break-word;
  word-break: break-all;
}
</style>
