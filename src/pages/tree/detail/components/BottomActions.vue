<template>
  <view class="bottom-actions">
    <!-- 咨询客服 -->
    <view class="contact-service" @click="handleContact">
      <image
        src="/static/contact-service-2.svg"
        mode="aspectFit"
        class="contact-icon"
      />
      <text class="contact-text">咨询客服</text>
    </view>

    <!-- 立即认养按钮 -->
    <view class="adopt-button" @click="handleAdopt">
      <text class="adopt-text">立即认养</text>
    </view>

    <!-- 认养模态框 -->
    <AdoptModal
      :visible="showAdoptModal"
      :productInfo="productInfo"
      @close="closeAdoptModal"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import AdoptModal from './AdoptModal.vue'

// Props
const props = defineProps({
  productInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['contact'])

// 响应式数据
const showAdoptModal = ref(false)

// 事件处理
const handleContact = () => {
  emit('contact')
}

const handleAdopt = () => {
  showAdoptModal.value = true
}

const closeAdoptModal = () => {
  showAdoptModal.value = false
}
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-gray: #666666;
$border-gray: #e9e9e9;
$shadow-color: rgba(0, 0, 0, 0.2);

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  background-color: $white-color;
  border-top: 1rpx solid $border-gray;
  box-shadow: 0 -3rpx 15rpx 0 $shadow-color;
  padding: 20rpx 30rpx;
  z-index: 100;
}

.contact-service {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .contact-icon {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 6rpx;
  }
  
  .contact-text {
    font-size: 22rpx;
    line-height: 26rpx;
    color: $text-gray;
    text-align: center;
  }
}

.adopt-button {
  width: 350rpx;
  height: 80rpx;
  background-color: $primary-color;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto; // 强制右对齐，解决小程序兼容性问题

  .adopt-text {
    font-size: 36rpx;
    font-weight: 500;
    line-height: 43rpx;
    color: $white-color;
  }
}
</style>
