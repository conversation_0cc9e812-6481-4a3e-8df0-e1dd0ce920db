<template>
  <view class="profile-page">
    <!-- 头像编辑区域 -->
    <view class="avatar-section">
      <view class="profile-item">
        <text class="item-label">头像</text>
        <view class="item-content">
          <button
            class="avatar-button"
            open-type="chooseAvatar"
            @chooseavatar="onChooseAvatar"
          >
            <view class="avatar-container">
              <image
                class="avatar-image"
                :src="userProfile.avatar"
                mode="aspectFill"
              />
            </view>
          </button>
        </view>
      </view>
    </view>

    <!-- 昵称编辑区域 -->
    <view class="nickname-section">
      <view class="profile-item">
        <text class="item-label">昵称</text>
        <view class="item-content">
          <input
            class="nickname-input"
            type="nickname"
            placeholder="请输入昵称"
            :value="userProfile.nickName"
            @input="onNicknameChange"
          />
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-section">
      <button
        class="save-button"
        @click="saveUserInfo"
        :disabled="isSaving"
      >
        {{ isSaving ? '保存中...' : '保存' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import { updateProfile, uploadFile } from '@/api/user'
import logoSvg from '@/static/logo.svg'

// 常量定义
const DEFAULT_AVATAR = logoSvg

const MESSAGES = {
  AVATAR_UPDATING: '更新中...',
  AVATAR_UPDATE_SUCCESS: '头像更新成功',
  AVATAR_UPDATE_FAILED: '头像更新失败，请重试',
  SAVING: '保存中...',
  SAVE_SUCCESS: '保存成功',
  SAVE_FAILED: '保存失败',
  NICKNAME_REQUIRED: '请输入昵称'
}

// 用户store
const userStore = useUserStore()

// 用户资料数据
const userProfile = ref({
  avatar: DEFAULT_AVATAR,
  nickName: ''
})

// 保存状态
const isSaving = ref(false)

// 显示加载提示
const showLoading = (title) => uni.showLoading({ title })

// 隐藏加载提示
const hideLoading = () => uni.hideLoading()

// 显示成功提示
const showSuccessToast = (title) => {
  uni.showToast({ title, icon: 'success' })
}

// 显示错误提示
const showErrorToast = (title) => {
  uni.showToast({ title, icon: 'none' })
}

// 获取用户信息
onLoad(async () => {
  try {
    // 优先使用store中的用户信息
    if (userStore.userInfo) {
      userProfile.value = { ...userStore.userInfo }
      // 如果头像为空，使用默认头像
      if (!userProfile.value.avatar || !userProfile.value.avatar.trim()) {
        userProfile.value.avatar = DEFAULT_AVATAR
      }
      console.log('使用store中的用户信息')
    } else {
      // 如果store中没有用户信息，则从服务器获取
      showLoading('加载中...')
      await userStore.refreshUserInfo()
      userProfile.value = { ...userStore.userInfo }
      // 如果头像为空，使用默认头像
      if (!userProfile.value.avatar || !userProfile.value.avatar.trim()) {
        userProfile.value.avatar = DEFAULT_AVATAR
      }
      hideLoading()
      console.log('从服务器获取用户信息')
    }
  } catch (error) {
    hideLoading()
    console.error('获取用户信息失败:', error)
    showErrorToast('加载失败，请稍后重试')
  }
})

// 选择头像处理函数
const onChooseAvatar = async (e) => {
  const avatarUrl = e.detail.avatarUrl

  try {
    showLoading('上传头像中...')

    // 立即上传头像文件，获取服务器URL
    const uploadResult = await uploadFile({ path: avatarUrl })

    // 更新本地预览，但不保存到用户资料
    userProfile.value.avatar = uploadResult.url

    hideLoading()
    showSuccessToast('头像上传成功')
  } catch (error) {
    hideLoading()
    console.error('头像上传失败:', error)
    showErrorToast(MESSAGES.AVATAR_UPDATE_FAILED)

    // 上传失败时，恢复原头像
    if (userStore.userInfo?.avatar) {
      userProfile.value.avatar = userStore.userInfo.avatar
    } else {
      userProfile.value.avatar = DEFAULT_AVATAR
    }
  }
}

// 昵称输入处理函数
const onNicknameChange = (e) => {
  userProfile.value.nickName = e.detail.value
}

// 保存用户信息
const saveUserInfo = async () => {
  if (!userProfile.value.nickName.trim()) {
    showErrorToast(MESSAGES.NICKNAME_REQUIRED)
    return
  }

  try {
    isSaving.value = true
    showLoading(MESSAGES.SAVING)

    const updateData = {
      nickName: userProfile.value.nickName
    }

    // 如果头像已上传且不是默认头像，包含在更新数据中
    if (userProfile.value.avatar && userProfile.value.avatar !== DEFAULT_AVATAR) {
      updateData.avatar = userProfile.value.avatar
    }

    // 调用更新接口
    await updateProfile(updateData)

    // 刷新用户信息，确保获取服务器端最新数据
    await userStore.refreshUserInfo()

    hideLoading()
    showSuccessToast(MESSAGES.SAVE_SUCCESS)

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    hideLoading()
    console.error('保存用户信息失败:', error)
    showErrorToast(MESSAGES.SAVE_FAILED)
  } finally {
    isSaving.value = false
  }
}
</script>

<style lang="scss" scoped>
// 样式变量
$white: #ffffff;
$text-dark: #333333;
$text-light: #666666;
$background-gray: #f5f5f5;
$gradient-start: #f1d292;
$gradient-end: #d28b23;
$primary-color: #dd3c29;
$primary-disabled: #ccc;

// 尺寸变量
$container-width: 710rpx;
$item-height: 100rpx;
$avatar-size: 64rpx;
$avatar-image-size: 56rpx;
$border-radius: 8rpx;
$padding: 32rpx;
$gap: 24rpx;
$button-height: 88rpx;

/* 个人资料页面 */
.profile-page {
  width: 100%;
  min-height: 100vh;
  background-color: $background-gray;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20rpx;
}

/* 头像区域 */
.avatar-section {
  width: $container-width;
  margin-bottom: 20rpx;
}

/* 昵称区域 */
.nickname-section {
  width: $container-width;
  margin-bottom: 40rpx;
}

/* 保存按钮区域 */
.save-section {
  width: $container-width;
  margin-top: 20rpx;
}

/* 通用资料项 */
.profile-item {
  height: $item-height;
  background-color: $white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $padding;
  border-radius: $border-radius;
}

/* 项目内容区域 */
.item-content {
  display: flex;
  align-items: center;
  gap: $gap;
}

/* 项目标签 */
.item-label {
  font-size: 32rpx;
  color: $text-dark;
  line-height: 38rpx;
}

/* 头像按钮 */
.avatar-button {
  background: transparent;
  padding: 0;
  border: none;

  &::after {
    border: none;
  }
}

/* 头像容器 */
.avatar-container {
  width: $avatar-size;
  height: $avatar-size;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid transparent;
  background: linear-gradient(180deg, $gradient-start 0%, $gradient-end 100%);
  background-clip: padding-box;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 头像图片 */
.avatar-image {
  width: $avatar-image-size;
  height: $avatar-image-size;
  border-radius: 50%;
}

/* 昵称输入框 */
.nickname-input {
  font-size: 28rpx;
  color: $text-light;
  line-height: 33rpx;
  border: none;
  background: transparent;
  text-align: right;
  width: 400rpx;

  &::placeholder {
    color: $text-light;
  }
}

/* 保存按钮 */
.save-button {
  width: 100%;
  height: $button-height;
  background-color: $primary-color;
  color: $white;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &::after {
    border: none;
  }

  &:disabled {
    background-color: $primary-disabled;
    color: $white;
  }

  &:active:not(:disabled) {
    opacity: 0.8;
  }
}
</style>
