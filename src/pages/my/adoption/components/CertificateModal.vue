<template>
  <view class="certificate-modal-overlay" @click.self="closeModal">
    <view class="modal-content">
      <view class="close-button-wrapper" @click="closeModal">
        <view class="close-button">
          <text class="close-button-text"> 返回 </text>
        </view>
      </view>

      <view class="certificate-container">
        <image class="certificate-bg" src="@/static/certificate-bg.jpg" mode="aspectFill" />
        <view class="certificate-content">
          <text class="certificate-title">认养证书</text>
          <text class="certificate-id">证书编号：JHK202505101212</text>

          <view class="adoptee-name-wrapper">
            <text class="adoptee-name">王安琪</text>
            <view class="name-underline"></view>
          </view>

          <view class="certificate-body">
            <text class="text-segment"> 恭喜您，成功您认养了位于 </text>
            <text class="text-segment-bold">  金壶口 </text>
            <text class="text-segment"> 的 </text>
            <text class="text-segment-bold"> 苹果树 </text>
            <text class="text-segment"> 编号为 </text>
            <text class="text-segment-red"> JHK202505101212 </text>
            <text class="text-segment">
              。<br />绿荫泽被，生生不息。 您的认养善举，不仅赋予了这棵树木特别的守护，更是为城市增添一抹生机，为大地播撒一份希望。感谢您对绿色家园的深情厚意，您的行动让我们的环境更加清新美好。<br />特发此证，以致谢忱！<br /><br />金壶口生态基金会<br />XXXX年XX月XX日)
            </text>
          </view>
        </view>
      </view>

      <text class="save-tip"> 长按图片保存到手机 </text>
    </view>
  </view>
</template>

<script setup>
const emit = defineEmits(['close']);

const closeModal = () => {
  emit('close');
};
</script>

<style lang="scss" scoped>
.certificate-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.close-button-wrapper {
  margin-bottom: 32rpx;
  align-self: flex-start;
  margin-left: 75rpx;
}

.close-button {
  width: 166rpx;
  height: 64rpx;
  border-radius: 326rpx;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-button-text {
  font-size: 30rpx;
  color: #ffffff;
}

.certificate-container {
  width: 600rpx;
  height: 900rpx;
  position: relative;
}

.certificate-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.certificate-content {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  box-sizing: border-box;
}

.certificate-title {
  margin-top: 64rpx;
  font-size: 64rpx;
  font-weight: 700;
  color: #dd3c29;
  line-height: 75rpx;
}

.certificate-id {
  margin-top: 16rpx;
  font-size: 22rpx;
  color: #dd3c29;
  line-height: 26rpx;
}

.adoptee-name-wrapper {
  margin-top: 56rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.adoptee-name {
  font-size: 36rpx;
  font-weight: 500;
  color: #1a1a1a;
  line-height: 42rpx;
}

.name-underline {
  width: 360rpx;
  height: 2rpx;
  background-color: rgba(221, 60, 41, 0.5);
  margin-top: 31rpx;
}

.certificate-body {
  margin-top: 31rpx;
  width: 436rpx;
  line-height: 40rpx;
  color: #3d3d3d;
}

.text-segment {
  font-size: 22rpx;
}
.text-segment-bold {
  font-size: 22rpx;
  font-weight: 700;
}
.text-segment-red {
  font-size: 22rpx;
  color: #dd3c29;
}

.save-tip {
  margin-top: 32rpx;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 28rpx;
}
</style>