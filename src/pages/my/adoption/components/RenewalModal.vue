<template>
  <view class="renewal-modal-overlay" @click.self="closeModal">
    <view class="renewal-modal-body">
      <view class="header-section">
        <view class="close-button-wrapper" @click="closeModal">
          <view class="close-button">
            <text class="close-button-text"> 返回 </text>
          </view>
        </view>
        <image class="tree-image" src="/static/flower-tree.png" mode="aspectFit" />
      </view>

      <view class="content-card">
        <view class="text-content">
          <text class="title"> 认养到期提醒 </text>
          <text class="greeting"> 亲爱的金壶口用户： </text>
          <view class="message-body">
            <text class="text-segment"> 亲爱的 </text>
            <text class="text-segment-bold"> 王安琪 </text>
            <text class="text-segment"> ：<br />时光如梭，您在“金壶口”绿色平台认养的专属果树 </text>
            <text class="text-segment-bold"> JHK202505101212号苹果树 </text>
            <text class="text-segment"> 陪伴您又走过了一个丰收的四季。看着它从萌芽、开花到挂满枝头，这份共同成长的喜悦，想必您也深有体会。<br />您的认养期将于 </text>
            <text class="text-segment-bold"> 2025年6月30日 </text>
            <text class="text-segment">
              结束。<br /><br />过去的日子，感谢您的悉心守护！您的支持不仅让这棵果树茁壮成长，也为我们共同的绿色家园增添了一份生机。枝头沉甸甸的果实，是您爱心的甜蜜回馈。<br /><br />别让这份专属的甜蜜中断！ 我们诚挚邀请您一键续费，继续成为这棵果树的守护者。
            </text>
          </view>
        </view>
        <view class="action-button-wrapper">
          <view class="action-button">
            <text class="action-button-text"> 再次认养 </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
const emit = defineEmits(['close']);

const closeModal = () => {
  emit('close');
};
</script>

<style lang="scss" scoped>
.renewal-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.renewal-modal-body {
  position: relative;
  width: 620rpx;
}

.header-section {
  position: absolute;
  top: -80rpx;
  left: 0;
  width: 100%;
  height: 322rpx;
  display: flex;
  justify-content: space-between;
  z-index: 2;
}

.tree-image {
  width: 100%;
  height: 100%;
  margin-right: -100rpx;
}

.close-button-wrapper {
}

.close-button {
  width: 166rpx;
  height: 64rpx;
  border-radius: 326rpx;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-button-text {
  font-size: 30rpx;
  color: #ffffff;
}

.content-card {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-sizing: border-box;
  z-index: 1;
  padding-top: 114rpx;
}

.text-content {
  /* Container for text elements */
}

.title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.greeting {
  display: block;
  margin-top: 40rpx;
  font-size: 24rpx;
  color: #333333;
}

.message-body {
  margin-top: 32rpx;
  text-align: justify;
  line-height: 40rpx;
  color: #333333;
}

.text-segment,
.text-segment-bold {
  font-size: 24rpx;
  line-height: 40rpx;
}

.text-segment-bold {
  font-weight: 700;
}

.action-button-wrapper {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}

.action-button {
  width: 520rpx;
  height: 80rpx;
  border-radius: 326rpx;
  background-color: #dd3c29;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-button-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
}
</style>