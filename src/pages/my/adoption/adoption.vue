<script setup>
import { ref, onMounted } from 'vue'
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import RenewalModal from './components/RenewalModal.vue'
import CertificateModal from './components/CertificateModal.vue'
import Tab from '@/components/Tab.vue'
import EmptyState from '@/components/EmptyState.vue'
import AdoptionCard from '@/components/AdoptionCard.vue'
import { getAdoptionList } from '@/api/order'

const activeTab = ref(0) // 0 for '认养中', 1 for '已结束'
const isRenewalModalVisible = ref(false)
const isCertificateModalVisible = ref(false)

const showRenewalModal = () => {
  isRenewalModalVisible.value = true
}

const hideRenewalModal = () => {
  isRenewalModalVisible.value = false
}

const showCertificateModal = () => {
  isCertificateModalVisible.value = true
}

const hideCertificateModal = () => {
  isCertificateModalVisible.value = false
}

// 响应式数据
const adoptionList = ref([])
const loading = ref(false)
const loadMoreStatus = ref('more') // more, loading, noMore
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)

// 加载更多文本配置
const loadMoreText = ref({
  contentdown: '上拉显示更多',
  contentrefresh: '正在加载...',
  contentnomore: '没有更多数据了'
})

// 加载数据
const loadData = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      pageNum.value = 1
      adoptionList.value = []
      hasMore.value = true
    }

    loading.value = true
    loadMoreStatus.value = 'loading'

    // Based on the UI, we assume '0' maps to 'ongoing' and '1' to 'finished'.
    // This should be confirmed with the actual API documentation.
    const statusMapping = ['ongoing', 'finished']

    const res = await getAdoptionList({
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      status: statusMapping[activeTab.value]
    })

    const { rows, total } = res

    if (isRefresh) {
      adoptionList.value = rows
    } else {
      adoptionList.value.push(...rows)
    }

    hasMore.value = adoptionList.value.length < total
    loadMoreStatus.value = hasMore.value ? 'more' : 'noMore'
    pageNum.value++
  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
    loadMoreStatus.value = 'more'
  } finally {
    loading.value = false
  }
}

// 下拉刷新处理函数
const handlePullDownRefresh = async () => {
  await loadData(true)
  uni.stopPullDownRefresh()
}

// 上拉加载更多处理函数
const handleReachBottom = () => {
  if (hasMore.value && loadMoreStatus.value !== 'loading') {
    loadData(false)
  }
}

// uni-load-more组件点击事件
const onLoadMore = () => {
  if (hasMore.value && loadMoreStatus.value !== 'loading') {
    loadData(false)
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadData(true)
})

// 注册页面生命周期
onPullDownRefresh(handlePullDownRefresh)
onReachBottom(handleReachBottom)

const switchTab = (tabIndex) => {
  activeTab.value = tabIndex
  loadData(true)
}

const navigateToEquity = () => {
  uni.navigateTo({
    url: '/pages/my/equity/equity'
  })
}

const navigateToAdopt = () => {
  uni.switchTab({
    url: '/pages/tree/tree'
  })
}
</script>

<template>
  <view class="page-container">
    <!-- Tabs Section -->
    <view class="tabs-section">
      <Tab text="认养中" :active="activeTab === 0" @click="switchTab(0)" />
      <Tab text="已结束" :active="activeTab === 1" @click="switchTab(1)" />
    </view>

    <!-- 空状态 -->
    <EmptyState
      v-if="!loading && adoptionList.length === 0"
      title="暂时还没有领养果树哦"
      button-text="认养果树"
      @button-click="navigateToAdopt"
    />

    <!-- Adoption Card List -->
    <AdoptionCard
      v-for="adoption in adoptionList"
      :key="adoption.id"
      :adoption="adoption"
      @navigate-to-equity="navigateToEquity"
      @show-certificate="showCertificateModal"
    />

    <!-- 加载更多组件 -->
    <uni-load-more
      v-if="adoptionList.length > 0"
      :status="loadMoreStatus"
      @clickLoadMore="onLoadMore"
      :content-text="loadMoreText"
    />

    <RenewalModal v-if="isRenewalModalVisible" @close="hideRenewalModal" />
    <CertificateModal v-if="isCertificateModalVisible" @close="hideCertificateModal" />
  </view>
</template>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 40rpx;
}

.tabs-section {
  width: 100%;
  height: 96rpx;
  background-color: #ffffff;
  border-bottom: 1.5rpx solid #eeeeee;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 100rpx;
}





</style>
