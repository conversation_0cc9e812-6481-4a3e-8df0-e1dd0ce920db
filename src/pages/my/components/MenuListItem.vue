<template>
  <view class="menu-list-item" @click="handleClick">
    <view class="item-content">
      <view class="icon-container">
        <image 
          :src="icon" 
          class="menu-icon"
          mode="aspectFit"
        />
      </view>
      <text class="menu-title">{{ title }}</text>
      <view class="arrow-container">
        <image
          src="/static/icons/arrow-right.svg"
          class="arrow-icon"
          mode="aspectFit"
        />
      </view>
    </view>
  </view>
</template>

<script setup>
const emit = defineEmits(['click'])

defineProps({
  icon: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  }
})

const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
// 颜色变量
$text-dark: #1a1a1a;
$border-gray: #e9e9e9;

.menu-list-item {
  .item-content {
    height: 100rpx;
    padding: 0 32rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
    border-bottom: 1rpx solid $border-gray;
    
    .icon-container {
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      
      .menu-icon {
        width: 100%;
        height: 100%;
      }
    }
    
    .menu-title {
      flex: 1;
      font-size: 30rpx;
      color: $text-dark;
      line-height: 35rpx;
    }
    
    .arrow-container {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .arrow-icon {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  &:last-child {
    .item-content {
      border-bottom: none;
    }
  }
}
</style>
