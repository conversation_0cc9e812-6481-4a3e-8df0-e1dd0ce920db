<template>
  <view class="login-container">
    <!-- Logo区域 -->
    <view class="logo-section">
      <view class="logo-container">
        <image class="logo-main" src="/static/logo.svg" mode="aspectFit" />
      </view>
    </view>

    <!-- 应用标题 -->
    <text class="app-title">延安果礼</text>

    <!-- 绑定手机号按钮区域 -->
    <view class="login-buttons">
      <!-- 未同意协议时显示的提示按钮 -->
      <view
        v-if="!isAgreed"
        class="primary-login-btn disabled-btn"
        @click="showAgreementTip"
      >
        <text class="primary-btn-text">绑定手机号</text>
      </view>

      <!-- 已同意协议时显示的功能按钮 -->
      <button
        v-else
        class="primary-login-btn"
        open-type="getPhoneNumber"
        @getphonenumber="handleGetPhoneNumber"
        :disabled="isBinding"
        :loading="isBinding"
        phone-number-no-quota-toast="true"
      >
        <text class="primary-btn-text">{{ isBinding ? '绑定中...' : '绑定手机号' }}</text>
      </button>
    </view>

    <!-- 协议同意区域 -->
    <view class="agreement-section" @click="toggleAgreement">
      <view class="checkbox-container">
        <image
          class="checkbox-icon"
          :src="isAgreed ? '/static/icons/checked.svg' : '/static/icons/unchecked.svg'"
          mode="aspectFit"
        />
      </view>
      <text class="agreement-text">
        <text class="normal-text">阅读并同意</text>
        <text class="link-text" @click.stop="showUserAgreement">用户协议、隐私政策</text>
      </text>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import { bindMobile } from '@/api/user'

// Store
const userStore = useUserStore()

// 响应式数据
const isAgreed = ref(false)
const fromPage = ref('') // 来源页面
const isBinding = ref(false) // 绑定加载状态

// 页面加载时获取来源页面参数并显示提示
onLoad((options) => {
  if (options.from) {
    fromPage.value = decodeURIComponent(options.from)
  }

  // 显示绑定手机号提示
  uni.showToast({
    title: '请先绑定手机号',
    icon: 'none',
    duration: 2000
  })
})

// 方法
const toggleAgreement = () => {
  isAgreed.value = !isAgreed.value
}

// 显示协议提示
const showAgreementTip = () => {
  uni.showToast({
    title: '请先同意用户协议',
    icon: 'none'
  })
}

// 处理获取手机号回调
const handleGetPhoneNumber = async (e) => {
  console.log('获取手机号回调:', e.detail)

  // 检查是否获取成功
  if (e.detail.errMsg !== 'getPhoneNumber:ok') {
    console.error('获取手机号失败:', e.detail.errMsg)

    // 处理特定错误码
    if (e.detail.errno === 1400001) {
      uni.showToast({
        title: '该功能使用次数已达上限',
        icon: 'none'
      })
    } else if (e.detail.errMsg.includes('cancel')) {
      // 用户取消授权，不显示错误提示
      return
    } else {
      uni.showToast({
        title: '获取手机号失败',
        icon: 'none'
      })
    }
    return
  }

  // 检查是否有动态令牌
  if (!e.detail.code) {
    uni.showToast({
      title: '获取授权码失败',
      icon: 'none'
    })
    return
  }

  try {
    // 设置加载状态
    isBinding.value = true

    // 调用绑定手机号API
    await bindMobile(e.detail.code)

    // 刷新用户信息
    await userStore.refreshUserInfo()

    uni.showToast({
      title: '绑定成功',
      icon: 'success'
    })

    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      navigateToTargetPage()
    }, 1500)

  } catch (error) {
    console.error('绑定手机号失败:', error)
    uni.showToast({
      title: error.message || '绑定失败',
      icon: 'none'
    })
  } finally {
    // 清除加载状态
    isBinding.value = false
  }
}

// 导航到目标页面
const navigateToTargetPage = () => {
  if (fromPage.value) {
    // 重定向到来源页面
    if (fromPage.value.includes('/pages/tree/tree')) {
      uni.switchTab({
        url: '/pages/tree/tree'
      })
    } else if (fromPage.value.includes('/pages/my/my')) {
      uni.switchTab({
        url: '/pages/my/my'
      })
    } else {
      uni.switchTab({
        url: '/pages/tower/tower'
      })
    }
  } else {
    // 默认跳转到首页
    uni.switchTab({
      url: '/pages/tower/tower'
    })
  }
}

// 暂不绑定
const skipBinding = () => {
  if (fromPage.value) {
    // 重定向到来源页面（但这会再次触发检查，所以实际上跳转到首页）
    uni.switchTab({
      url: '/pages/tower/tower'
    })
  } else {
    // 默认跳转到首页
    uni.switchTab({
      url: '/pages/tower/tower'
    })
  }
}

const showUserAgreement = () => {
  // 显示用户协议页面
  uni.navigateTo({
    url: '/pages/agreement/agreement'
  })
}
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-gray: #666666;
$border-gray: #999999;

$button-width: 670rpx;
$button-height: 88rpx;
$button-radius: 326rpx;
$logo-size: 240rpx;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin button-base {
  width: $button-width;
  height: $button-height;
  border-radius: $button-radius;
  @include flex-center;
}

@mixin button-text {
  font-size: 32rpx;
  line-height: 38rpx;
}

// 登录页面容器
.login-container {
  min-height: 100vh;
  background-color: $white-color;
  display: flex;
  flex-direction: column;
  position: relative;
  padding-top: 50rpx;
}

// Logo区域
.logo-section {
  margin-top: 147rpx;
  @include flex-center;

  .logo-container {
    @include flex-center;

    .logo-main {
      width: $logo-size;
      height: $logo-size;
    }
  }
}

// 应用标题
.app-title {
  text-align: center;
  font-size: 48rpx;
  font-weight: 400;
  line-height: 56rpx;
  color: $primary-color;
  margin-top: 0;
  align-self: center;
}

// 登录按钮区域
.login-buttons {
  margin-top: 147rpx;
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .primary-login-btn {
    @include button-base;
    background-color: $primary-color;
    margin-bottom: 40rpx;
    border: none;
    padding: 0;

    // 禁用状态样式
    &[disabled] {
      background-color: rgba($primary-color, 0.5);
      color: rgba($white-color, 0.7);
    }

    // 重置button默认样式
    &::after {
      border: none;
    }

    .primary-btn-text {
      @include button-text;
      font-weight: 500;
      color: $white-color;
    }
  }

  // 禁用按钮样式（未同意协议时）
  .disabled-btn {
    background-color: rgba($primary-color, 0.5) !important;

    .primary-btn-text {
      color: rgba($white-color, 0.7) !important;
    }
  }

  .secondary-login-btn {
    @include button-base;
    border: 2rpx solid rgba($primary-color, 0.5);
    background-color: $white-color;

    .secondary-btn-text {
      @include button-text;
      font-weight: 400;
      color: $primary-color;
    }
  }
}

// 协议同意区域
.agreement-section {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin: 60rpx 40rpx 40rpx;

  .checkbox-container {
    margin-right: 17rpx;

    .checkbox-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .agreement-text {
    line-height: 30rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 2rpx;

    .normal-text {
      font-size: 26rpx;
      font-weight: 400;
      color: $text-gray;
    }

    .link-text {
      font-size: 26rpx;
      font-weight: 400;
      color: $primary-color;
    }
  }
}
</style>
