<template>
  <view class="adoption-card">
    <view class="card-header">
      <image class="product-image" :src="adoption.fruitImage || '/static/images/default-fruit.png'" mode="aspectFill" />
      <view class="product-info">
        <text class="product-title">{{ adoption.fruitTreeName }}</text>
        <text class="product-specs">{{ adoption.fruitDesc }}</text>
      </view>
    </view>

    <view class="card-body">
      <ProgressBar :percentage="progressPercentage" />

      <view class="date-info">
        <text class="date-text">{{ adoption.startDate }}购买</text>
        <text class="date-text">{{ adoption.endDate }}采摘</text>
      </view>

      <view class="adoption-status">
        <view>
          <text class="status-highlight">{{ statusText }}</text>
          <text class="status-text">{{ statusDescription }}</text>
        </view>
        <text class="status-remark">{{ statusRemark }}</text>
      </view>

      <view class="certificate-info">
        <view class="buttons">
          <view class="equity-btn" @click="handleEquityClick">
            <text class="equity-btn-text">我的权益</text>
          </view>
          <view class="view-certificate-btn" @click="handleCertificateClick">
            <text class="btn-text">查看证书</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import ProgressBar from '@/components/ProgressBar.vue'

const emit = defineEmits(['navigate-to-equity', 'show-certificate'])

const props = defineProps({
  adoption: {
    type: Object,
    required: true
  }
})

// 计算进度百分比（基于开始和结束日期）
const progressPercentage = computed(() => {
  const startDate = new Date(props.adoption.startDate)
  const endDate = new Date(props.adoption.endDate)
  const currentDate = new Date()

  if (currentDate < startDate) return 0
  if (currentDate > endDate) return 100

  const totalDays = (endDate - startDate) / (1000 * 60 * 60 * 24)
  const passedDays = (currentDate - startDate) / (1000 * 60 * 60 * 24)

  return Math.round((passedDays / totalDays) * 100)
})

// 状态文本
const statusText = computed(() => {
  const percentage = progressPercentage.value
  if (percentage === 100) return '已完成'
  if (percentage > 80) return '即将收获'
  if (percentage > 50) return '茁壮成长'
  return '刚刚开始'
})

// 状态描述
const statusDescription = computed(() => {
  const startDate = new Date(props.adoption.startDate)
  const currentDate = new Date()
  const passedDays = Math.floor((currentDate - startDate) / (1000 * 60 * 60 * 24))
  const passedWeeks = Math.floor(passedDays / 7)

  return `已领养 ${passedWeeks}周 距离目标越来越近了`
})

// 状态备注
const statusRemark = computed(() => {
  return '让静待花开，等待收获的美满和小确幸'
})

const handleEquityClick = () => {
  emit('navigate-to-equity')
}

const handleCertificateClick = () => {
  emit('show-certificate')
}
</script>

<style lang="scss" scoped>
.adoption-card {
  width: 686rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.product-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.product-info {
  display: flex;
  flex-direction: column;
  margin-left: 24rpx;
  padding-top: 10rpx;
}

.product-title {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  color: #1a1a1a;
}

.product-specs {
  font-size: 30rpx;
  line-height: 40rpx;
  color: #999999;
  margin-top: 16rpx;
}

.card-body {
  margin-top: 32rpx;
  width: 100%;
}

.date-info {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
}

.date-text {
  font-size: 24rpx;
  line-height: 28rpx;
  color: #1a1a1a;
}

.adoption-status {
  width: 100%;
  margin-top: 24rpx;
  padding: 20rpx 32rpx;
  background-color: #fef3f2;
  border: 1rpx solid #ffbaba;
  border-radius: 12rpx;
  box-sizing: border-box;
}

.status-highlight {
  font-size: 36rpx;
  font-weight: 700;
  line-height: 42rpx;
  color: #dd3c29;
  margin-right: 16rpx;
}

.status-text {
  font-size: 26rpx;
  line-height: 30rpx;
  color: #1a1a1a;
}

.status-remark {
  display: block;
  margin-top: 16rpx;
  font-size: 24rpx;
  line-height: 28rpx;
  color: #767676;
}

.certificate-info {
  margin-top: 24rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}



.buttons {
  display: flex;
  gap: 16rpx;
}

.equity-btn {
  width: 166rpx;
  height: 64rpx;
  background-color: #ffffff;
  border: 1rpx solid #dd3c29;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    opacity: 0.8;
  }
}

.equity-btn-text {
  font-size: 30rpx;
  line-height: 35rpx;
  color: #dd3c29;
}

.view-certificate-btn {
  width: 166rpx;
  height: 64rpx;
  background-color: #dd3c29;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    opacity: 0.8;
  }
}

.btn-text {
  font-size: 30rpx;
  line-height: 35rpx;
  color: #ffffff;
}
</style>
