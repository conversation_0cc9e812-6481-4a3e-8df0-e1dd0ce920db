<template>
  <view class="adoption-card">
    <view class="card-header">
      <image class="product-image" :src="adoption.imageUrl" mode="aspectFill" />
      <view class="product-info">
        <text class="product-title">{{ adoption.title }}</text>
        <text class="product-specs">{{ adoption.subtitle }}</text>
      </view>
    </view>

    <view class="card-body">
      <ProgressBar :percentage="adoption.progress.percentage" />

      <view class="date-info">
        <text class="date-text">{{ adoption.purchaseDate }}购买</text>
        <text class="date-text">{{ adoption.expectedHarvestDate }}采摘</text>
      </view>

      <view class="adoption-status">
        <view>
          <text class="status-highlight">{{ adoption.progress.text.split(' ')[1] }}</text>
          <text class="status-text">{{ adoption.progress.text }}</text>
        </view>
        <text class="status-remark">{{ adoption.progress.secondaryText }}</text>
      </view>

      <view class="certificate-info">
        <text class="certificate-id">证书编号：{{ adoption.certificateId }}</text>
        <view class="buttons">
          <view class="equity-btn" @click="handleEquityClick">
            <text class="equity-btn-text">我的权益</text>
          </view>
          <view class="view-certificate-btn" @click="handleCertificateClick">
            <text class="btn-text">查看证书</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import ProgressBar from '@/components/ProgressBar.vue'

const emit = defineEmits(['navigate-to-equity', 'show-certificate'])

defineProps({
  adoption: {
    type: Object,
    required: true
  }
})

const handleEquityClick = () => {
  emit('navigate-to-equity')
}

const handleCertificateClick = () => {
  emit('show-certificate')
}
</script>

<style lang="scss" scoped>
.adoption-card {
  width: 686rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.product-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.product-info {
  display: flex;
  flex-direction: column;
  margin-left: 24rpx;
  padding-top: 10rpx;
}

.product-title {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  color: #1a1a1a;
}

.product-specs {
  font-size: 30rpx;
  line-height: 40rpx;
  color: #999999;
  margin-top: 16rpx;
}

.card-body {
  margin-top: 32rpx;
  width: 100%;
}

.date-info {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
}

.date-text {
  font-size: 24rpx;
  line-height: 28rpx;
  color: #1a1a1a;
}

.adoption-status {
  width: 100%;
  margin-top: 24rpx;
  padding: 20rpx 32rpx;
  background-color: #fef3f2;
  border: 1rpx solid #ffbaba;
  border-radius: 12rpx;
  box-sizing: border-box;
}

.status-highlight {
  font-size: 36rpx;
  font-weight: 700;
  line-height: 42rpx;
  color: #dd3c29;
  margin-right: 16rpx;
}

.status-text {
  font-size: 26rpx;
  line-height: 30rpx;
  color: #1a1a1a;
}

.status-remark {
  display: block;
  margin-top: 16rpx;
  font-size: 24rpx;
  line-height: 28rpx;
  color: #767676;
}

.certificate-info {
  margin-top: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.certificate-id {
  font-size: 26rpx;
  line-height: 30rpx;
  color: #1a1a1a;
}

.buttons {
  display: flex;
  gap: 16rpx;
}

.equity-btn {
  width: 166rpx;
  height: 64rpx;
  background-color: #ffffff;
  border: 1rpx solid #dd3c29;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    opacity: 0.8;
  }
}

.equity-btn-text {
  font-size: 30rpx;
  line-height: 35rpx;
  color: #dd3c29;
}

.view-certificate-btn {
  width: 166rpx;
  height: 64rpx;
  background-color: #dd3c29;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    opacity: 0.8;
  }
}

.btn-text {
  font-size: 30rpx;
  line-height: 35rpx;
  color: #ffffff;
}
</style>
