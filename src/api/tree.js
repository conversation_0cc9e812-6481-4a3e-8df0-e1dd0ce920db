import request from '@/libs/http'


/**
 * @typedef {object} FruitTreeSummary
 * @property {string} id
 * @property {string} name
 * @property {string} imageUrl
 * @property {string} description
 * @property {number} price
 * @property {number} stock
 */

/**
 * @typedef {object} FruitTreeListResponse
 * @property {FruitTreeSummary[]} rows
 * @property {number} total
 */

/**
 * 获取果树列表
 * @param {object} [params]
 * @param {number} [params.pageNum]
 * @param {number} [params.pageSize]
 * @returns {Promise<FruitTreeListResponse>}
 */
export function getFruitTreeList(params) {
  return request({
    url: '/app/trees',
    method: 'GET',
    params
  })
}

/**
 * @typedef {object} Benefit
 * @property {string} icon
 * @property {string} title
 * @property {string} text
 */

/**
 * @typedef {object} ProductDetails
 * @property {string} brand
 * @property {string} productType
 * @property {string} diameter
 * @property {string} shelfLife
 * @property {string} origin
 * @property {string} packaging
 * @property {string} storage
 * @property {string} variety
 * @property {string} shippingTime
 * @property {string} adoptionPeriod
 * @property {string} sunshineHours
 */

/**
 * @typedef {object} FruitTree
 * @property {string} id
 * @property {string} name
 * @property {string} description
 * @property {string[]} imageUrls
 * @property {number} price
 * @property {number} salesCount
 * @property {Benefit[]} benefits
 * @property {ProductDetails} details
 */

/**
 * 获取果树详情
 * @param {string} id
 * @returns {Promise<FruitTree>}
 */
export function getFruitTreeDetail(id) {
  return request({
    url: `/app/trees/${id}`,
    method: 'GET'
  })
}
